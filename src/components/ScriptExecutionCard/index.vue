<template>
  <div class="script-execution-card">
    <div class="card-header">
      <SvgIcon icon="material-symbols:terminal" class="header-icon" />
      <span class="header-title">脚本执行任务</span>
      <div class="status-badge" :class="statusClass">
        <SvgIcon :icon="statusIcon" class="status-icon" />
        <span>{{ statusText }}</span>
      </div>
    </div>
    
    <div class="card-content">
      <div class="summary-section">
        <h4>任务摘要</h4>
        <p class="summary-text">{{ taskSummary }}</p>
      </div>
      
      <div class="task-info-section">
        <div class="info-item">
          <span class="info-label">任务ID:</span>
          <span class="info-value">{{ taskId }}</span>
        </div>
        <div class="info-item">
          <span class="info-label">创建时间:</span>
          <span class="info-value">{{ createTime }}</span>
        </div>
        <div class="info-item" v-if="executionTime">
          <span class="info-label">执行时间:</span>
          <span class="info-value">{{ executionTime }}</span>
        </div>
      </div>
      
      <div class="action-section">
        <n-button 
          type="primary" 
          size="medium"
          @click="showDetailModal = true"
          class="detail-btn"
        >
          <template #icon>
            <SvgIcon icon="material-symbols:visibility" />
          </template>
          查看详细信息
        </n-button>
        
        <n-button 
          v-if="!isExecuting && !isCompleted"
          type="success" 
          size="medium"
          :loading="isExecuting"
          @click="handleExecute"
          class="execute-btn"
        >
          <template #icon>
            <SvgIcon icon="material-symbols:play-arrow" />
          </template>
          开始执行
        </n-button>
      </div>
      
      <div v-if="isExecuting" class="progress-section">
        <n-progress 
          type="line" 
          :percentage="progress" 
          :show-indicator="true"
          status="info"
        />
        <p class="progress-text">正在执行脚本任务，请稍候...</p>
      </div>
    </div>
    
    <!-- 详细信息弹框 -->
    <n-modal 
      v-model:show="showDetailModal" 
      preset="card"
      title="脚本执行详情"
      size="large"
      :bordered="false"
      :segmented="true"
      class="detail-modal"
    >
      <div class="modal-content">
        <div class="detail-section">
          <h4>执行计划</h4>
          <div class="detail-content" v-html="formattedExecutionPlan"></div>
        </div>
        
        <div class="detail-section" v-if="executionSteps.length > 0">
          <h4>执行步骤</h4>
          <div class="steps-list">
            <div 
              v-for="(step, index) in executionSteps" 
              :key="index"
              class="step-item"
              :class="step.status"
            >
              <div class="step-icon">
                <SvgIcon :icon="getStepIcon(step.status)" />
              </div>
              <div class="step-content">
                <div class="step-title">{{ step.title }}</div>
                <div class="step-description">{{ step.description }}</div>
                <div v-if="step.result" class="step-result">{{ step.result }}</div>
              </div>
            </div>
          </div>
        </div>
        
        <div class="detail-section" v-if="executionResult">
          <h4>执行结果</h4>
          <div class="result-content" v-html="formattedExecutionResult"></div>
        </div>
      </div>
    </n-modal>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { NButton, NProgress, NModal } from 'naive-ui'
import SvgIcon from '@/components/common/SvgIcon/index.vue'

const props = defineProps({
  taskData: {
    type: Object,
    required: true
  },
  userMessage: {
    type: String,
    required: true
  }
})

const emit = defineEmits(['execution-complete'])

// 响应式数据
const showDetailModal = ref(false)
const isExecuting = ref(false)
const isCompleted = ref(false)
const progress = ref(0)
const executionTime = ref('')
const executionResult = ref('')
const executionSteps = ref([])

// 任务基本信息
const taskId = ref(`SCRIPT_${Date.now()}`)
const createTime = ref(new Date().toLocaleString())

// 计算属性
const taskSummary = computed(() => {
  return props.taskData.summary || '执行网络安全三同步脚本，包括渗透测试、漏洞扫描、安全设备有效性验证等操作。'
})

const statusClass = computed(() => {
  if (isExecuting.value) return 'executing'
  if (isCompleted.value) return 'completed'
  return 'pending'
})

const statusText = computed(() => {
  if (isExecuting.value) return '执行中'
  if (isCompleted.value) return '已完成'
  return '待执行'
})

const statusIcon = computed(() => {
  if (isExecuting.value) return 'material-symbols:sync'
  if (isCompleted.value) return 'material-symbols:check-circle'
  return 'material-symbols:schedule'
})

const formattedExecutionPlan = computed(() => {
  return formatContent(props.taskData.executionPlan || props.taskData.response || '')
})

const formattedExecutionResult = computed(() => {
  return formatContent(executionResult.value)
})

// 格式化内容
const formatContent = (content) => {
  if (!content) return ''
  
  return content
    .replace(/\n/g, '<br>')
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    .replace(/\*(.*?)\*/g, '<em>$1</em>')
    .replace(/`(.*?)`/g, '<code>$1</code>')
    .replace(/(🔍|🛡️|✅|📋|📊|🚨|⚠️|🎯|⚡|🔄|📧)/g, '<span class="emoji">$1</span>')
}

// 获取步骤图标
const getStepIcon = (status) => {
  switch (status) {
    case 'completed': return 'material-symbols:check-circle'
    case 'executing': return 'material-symbols:sync'
    case 'failed': return 'material-symbols:error'
    default: return 'material-symbols:schedule'
  }
}

// 模拟执行步骤
const mockExecutionSteps = [
  {
    title: '渗透测试',
    description: '邮件通知安服部门进行渗透测试',
    status: 'pending'
  },
  {
    title: '漏洞扫描',
    description: '调用绿盟扫描器对资产进行漏扫',
    status: 'pending'
  },
  {
    title: '安全设备验证',
    description: '调用BAS对资产进行有效性验证',
    status: 'pending'
  }
]

// 处理执行
const handleExecute = async () => {
  if (isExecuting.value || isCompleted.value) return
  
  isExecuting.value = true
  progress.value = 0
  executionTime.value = new Date().toLocaleString()
  executionSteps.value = [...mockExecutionSteps]
  
  try {
    // 模拟执行过程
    for (let i = 0; i < executionSteps.value.length; i++) {
      const step = executionSteps.value[i]
      step.status = 'executing'
      
      // 模拟执行时间
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      step.status = 'completed'
      step.result = `${step.title}执行完成`
      progress.value = ((i + 1) / executionSteps.value.length) * 100
    }
    
    // 执行完成
    isExecuting.value = false
    isCompleted.value = true
    executionResult.value = `**执行完成**

**🔍 1. 渗透测试结果**
- 测试完成，未发现高危漏洞
- 报告已生成：PENTEST_${Date.now()}

**🛡️ 2. 漏洞扫描结果**
- 扫描完成，发现1个中危漏洞
- 工单已派发：AH-074-250731-12

**✅ 3. 安全设备验证结果**
- 验证完成，所有设备运行正常
- 报告已生成：BAS_${Date.now()}

**📊 汇总结果**
- 总体安全状态：良好
- 需要关注的问题：1个
- 建议处理时间：24小时内`
    
    // 通知父组件
    emit('execution-complete', {
      success: true,
      taskId: taskId.value,
      result: executionResult.value
    })
    
  } catch (error) {
    console.error('执行失败:', error)
    isExecuting.value = false
    executionResult.value = '执行失败，请重试或联系管理员'
    
    emit('execution-complete', {
      success: false,
      taskId: taskId.value,
      result: '执行失败，请重试或联系管理员'
    })
  }
}

onMounted(() => {
  // 初始化执行步骤
  executionSteps.value = [...mockExecutionSteps]
})
</script>

<style lang="scss" scoped>
.script-execution-card {
  max-width: 600px;
  border: 1px solid #e0e0e6;
  border-radius: 12px;
  background: #ffffff;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
  color: white;
  
  .header-left {
    display: flex;
    align-items: center;
  }
  
  .header-icon {
    font-size: 20px;
    margin-right: 8px;
  }
  
  .header-title {
    font-size: 16px;
    font-weight: 600;
  }
  
  .status-badge {
    display: flex;
    align-items: center;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    
    .status-icon {
      font-size: 14px;
      margin-right: 4px;
    }
    
    &.pending {
      background: rgba(255, 255, 255, 0.2);
      color: #fbbf24;
    }
    
    &.executing {
      background: rgba(255, 255, 255, 0.2);
      color: #60a5fa;
      
      .status-icon {
        animation: spin 1s linear infinite;
      }
    }
    
    &.completed {
      background: rgba(255, 255, 255, 0.2);
      color: #34d399;
    }
  }
}

.card-content {
  padding: 20px;
}

.summary-section {
  margin-bottom: 20px;

  h4 {
    margin: 0 0 8px 0;
    font-size: 14px;
    font-weight: 600;
    color: #333;
  }

  .summary-text {
    margin: 0;
    font-size: 13px;
    line-height: 1.5;
    color: #666;
    background: #f8f9fa;
    padding: 12px;
    border-radius: 6px;
    border-left: 3px solid #4f46e5;
  }
}

.task-info-section {
  margin-bottom: 20px;
  background: #f9fafb;
  border-radius: 8px;
  padding: 16px;

  .info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;

    &:last-child {
      margin-bottom: 0;
    }

    .info-label {
      font-size: 13px;
      color: #6b7280;
      font-weight: 500;
    }

    .info-value {
      font-size: 13px;
      color: #374151;
      font-weight: 600;
      font-family: 'Monaco', 'Menlo', monospace;
    }
  }
}

.action-section {
  display: flex;
  gap: 12px;
  margin-bottom: 20px;

  .detail-btn, .execute-btn {
    flex: 1;
    height: 36px;
    font-size: 13px;
    font-weight: 500;
  }
}

.progress-section {
  margin-bottom: 20px;

  .progress-text {
    margin: 8px 0 0 0;
    font-size: 12px;
    color: #666;
    text-align: center;
  }
}

// 弹框样式
:deep(.detail-modal) {
  .n-card {
    max-width: 800px;
  }
}

.modal-content {
  max-height: 600px;
  overflow-y: auto;
}

.detail-section {
  margin-bottom: 24px;

  &:last-child {
    margin-bottom: 0;
  }

  h4 {
    margin: 0 0 12px 0;
    font-size: 16px;
    font-weight: 600;
    color: #1f2937;
    border-bottom: 2px solid #e5e7eb;
    padding-bottom: 8px;
  }

  .detail-content {
    font-size: 14px;
    line-height: 1.6;
    color: #374151;

    :deep(strong) {
      font-weight: 600;
      color: #1f2937;
    }

    :deep(code) {
      background: #f3f4f6;
      padding: 2px 4px;
      border-radius: 3px;
      font-family: 'Monaco', 'Menlo', monospace;
      font-size: 0.9em;
    }

    :deep(.emoji) {
      font-size: 1.1em;
    }
  }

  .result-content {
    background: #f0f9ff;
    border: 1px solid #bae6fd;
    border-radius: 8px;
    padding: 16px;
    font-size: 14px;
    line-height: 1.6;

    :deep(strong) {
      font-weight: 600;
      color: #0369a1;
    }

    :deep(.emoji) {
      font-size: 1.1em;
    }
  }
}

.steps-list {
  .step-item {
    display: flex;
    align-items: flex-start;
    padding: 12px;
    margin-bottom: 8px;
    border-radius: 8px;
    border: 1px solid #e5e7eb;
    transition: all 0.3s ease;

    &.pending {
      background: #f9fafb;
      border-color: #d1d5db;
    }

    &.executing {
      background: #eff6ff;
      border-color: #3b82f6;

      .step-icon {
        color: #3b82f6;

        svg {
          animation: spin 1s linear infinite;
        }
      }
    }

    &.completed {
      background: #f0fdf4;
      border-color: #22c55e;

      .step-icon {
        color: #22c55e;
      }
    }

    &.failed {
      background: #fef2f2;
      border-color: #ef4444;

      .step-icon {
        color: #ef4444;
      }
    }

    .step-icon {
      width: 20px;
      height: 20px;
      margin-right: 12px;
      margin-top: 2px;
      flex-shrink: 0;
    }

    .step-content {
      flex: 1;

      .step-title {
        font-size: 14px;
        font-weight: 600;
        color: #1f2937;
        margin-bottom: 4px;
      }

      .step-description {
        font-size: 13px;
        color: #6b7280;
        margin-bottom: 4px;
      }

      .step-result {
        font-size: 12px;
        color: #059669;
        font-weight: 500;
      }
    }
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
