/**
 * 关键词匹配模块
 * 用于处理特定关键词的自动回复功能，包括打字机效果、消息处理等
 */

// import { nextTick } from 'vue'
// 在非 Vue 环境中使用 Promise.resolve().then() 模拟 nextTick
const nextTick = (callback) => {
  if (typeof callback === 'function') {
    return Promise.resolve().then(callback)
  }
  return Promise.resolve()
}

// 打字机速度配置
const typingConfig = {
  baseSpeed: 150,        // 基础速度（毫秒）
  chineseSpeed: 100,     // 中文字符速度
  englishSpeed: 45,      // 英文数字速度
  spaceSpeed: 40,        // 空格速度
  markdownSpeed: 30,     // Markdown符号速度
  commaDelay: 300,       // 逗号停顿
  periodDelay: 400,      // 句号停顿
  newlineDelay: 1050     // 换行停顿
}

// 关键词匹配配置
const keywordResponses = [
  {
    keywords: ['4A系统所有欧拉操作系统的主机和数据库主机要做升级改造'],
    response: `您好！已收到您关于7月31日4A系统欧拉操作系统及数据库主机升级改造的网络安全"三
同步"（同步规划、同步建设、同步运行）需求。经查询资产库，4A系统升级涉及资产IP
清单如下：
请确认资产清单是否准确？`
  },
  {
    keywords: ['准确的，请帮我于', '进行网络安全三同步操作'],
    response: `根据《中国移动网络安全三同步管理办法》要求，以下是具体执行计划：

**1. 渗透测试**
- 时间节点：2025年7月31日4:30
- 执行内容：邮件通知安服部门进行渗透测试，并输出报告

**2. 漏洞扫描**
- 时间节点：2025年7月31日4:32
- 执行内容：调用绿盟扫描器对资产进行漏扫并输出报告，如有风险资产通过EOMS派发工单

**3. 安全设备有效性验证**
- 时间节点：2025年7月31日4:35
- 执行内容：调用BAS对资产进行有效性验证并输出报告

请确认是否要执行以上计划？`
  },
  {
    keywords: ['请帮我执行计划', '并将汇总结果发我'],
    response: `好的，根据您的需求正在执行计划1、2、3

      **1. 执行渗透测试剧本，输出报告**

      **2. 执行漏洞扫描剧本，输出报告**
      漏洞扫描已完成
      经漏扫发现有风险资产，已派发工单
      工单ID: AH-074-250731-12

      **3. 执行安全设备有效性验证剧本，输出报告**

      **汇总结果如下：**
      详细信息请查看各项报告，如有疑问请联系安全运维团队。`,
    needsAIProcessing: true // 标记需要AI进一步处理
  },
  // 第二个场景

  {
    keywords: ['态势感知平台监测到勒索病毒告警', '原始告警日志如下'],
    response: `【自动化威胁简报】
    ​事件等级:危急（CVSS9.8|影响核心财务服务器）
    ​攻击摘要:
    ​病毒类型:LockBit变种（通过CVE-2025-32745入侵）
    ​加密范围:2,847个财务文件（扩展名已变更为.xls.encrypted）
    ​横向移动:检测到Pass-the-Hash攻击尝试（目标:*************-50）
    关键处置建议:
    1.立即隔离：
    阻断主机SRV-FINANCE-01所有网络连接
    禁用账户dev_ops和admin（检测到异常令牌使用）
    2.取证留存：
    内存转储（已检测CobaltStrike痕迹）
    保留加密样本SHA-256
    3.恢复路径:从备份BK_20240804_0200优先恢复C:\Financial_Data\
    是否执行以下操作？​​
    1.隔离主机
    2.禁用账户
    3.启动备份恢复
    4.向上级发送告警摘要（含比特币地址和C2IP）
    5.修补漏洞CVE-2025-32745和CVE-2025-11233`
  },

  {
    keywords: ['确认执行所有操作'],
    response: `实时执行
    1.隔离完成
    2.账号禁用已完成
    3.备份恢复进行中
    4.工单已派发
    5.修补漏洞邮件已发送`
  },
]

/**
 * 检测消息是否匹配关键词
 * @param {string} message - 用户输入的消息
 * @returns {Object|null} 匹配的配置对象，如果没有匹配则返回 null
 */
export function matchKeywords(message) {
  if (!message || typeof message !== 'string') {
    return null
  }

  for (const config of keywordResponses) {
    const hasMatch = config.keywords.some(keyword =>
      message.includes(keyword)
    )
    if (hasMatch) {
      return config
    }
  }
  return null
}

/**
 * 检测是否为处置消息
 * @param {string} message - 用户输入的消息
 * @returns {boolean} 是否为处置消息
 */
export function isDisposalMessage(message) {
  if (!message || typeof message !== 'string') {
    return false
  }
  return message.startsWith('处置：')
}

/**
 * 获取所有关键词配置（用于调试或管理）
 * @returns {Array} 关键词配置数组
 */
export function getKeywordConfigs() {
  return [...keywordResponses] // 返回副本，避免外部修改
}

/**
 * 添加新的关键词配置
 * @param {Object} config - 新的关键词配置
 * @param {Array<string>} config.keywords - 关键词数组
 * @param {string} config.response - 回复内容
 * @param {boolean} [config.needsAIProcessing] - 是否需要AI进一步处理
 */
export function addKeywordConfig(config) {
  if (!config || !config.keywords || !config.response) {
    throw new Error('关键词配置格式错误')
  }
  
  if (!Array.isArray(config.keywords) || config.keywords.length === 0) {
    throw new Error('关键词必须是非空数组')
  }
  
  if (typeof config.response !== 'string' || config.response.trim() === '') {
    throw new Error('回复内容不能为空')
  }
  
  keywordResponses.push({
    keywords: [...config.keywords], // 创建副本
    response: config.response,
    needsAIProcessing: Boolean(config.needsAIProcessing)
  })
}

/**
 * 移除关键词配置
 * @param {number} index - 要移除的配置索引
 */
export function removeKeywordConfig(index) {
  if (index >= 0 && index < keywordResponses.length) {
    keywordResponses.splice(index, 1)
  }
}

/**
 * 清空所有关键词配置
 */
export function clearKeywordConfigs() {
  keywordResponses.length = 0
}

/**
 * 检查是否启用关键词匹配功能
 * 可以通过环境变量或配置文件控制
 * @returns {boolean} 是否启用关键词匹配
 */
export function isKeywordMatchingEnabled() {
  // 可以通过环境变量控制
  if (typeof window !== 'undefined' && window.DISABLE_KEYWORD_MATCHING) {
    return false
  }
  
  // 默认启用
  return true
}

/**
 * 主要的关键词处理函数
 * 这是外部调用的主要接口
 * @param {string} message - 用户输入的消息
 * @returns {Object|null} 处理结果
 */
export function processKeywordMessage(message) {
  // 检查功能是否启用
  if (!isKeywordMatchingEnabled()) {
    return null
  }

  // 检查是否为处置消息
  if (isDisposalMessage(message)) {
    return {
      type: 'disposal',
      message: message
    }
  }

  // 检查关键词匹配
  const keywordMatch = matchKeywords(message)
  if (keywordMatch) {
    return {
      type: 'keyword',
      config: keywordMatch,
      message: message
    }
  }

  return null
}

/**
 * 格式化打字内容（保持换行和格式）
 * @param {string} content - 要格式化的内容
 * @returns {string} 格式化后的HTML内容
 */
export function formatTypingContent(content) {
  return content
    .replace(/\n/g, '<br>')
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    .replace(/\*(.*?)\*/g, '<em>$1</em>')
    .replace(/`(.*?)`/g, '<code>$1</code>')
}

/**
 * 显示打字机效果的回复
 * @param {string} responseText - 回复文本
 * @param {Object} chatContext - 聊天上下文对象
 * @param {Function} chatContext.addMessage - 添加消息的函数
 * @param {Function} chatContext.updateMessage - 更新消息的函数
 * @param {Function} chatContext.saveMessages - 保存消息的函数
 * @param {Function} chatContext.scrollToBottom - 滚动到底部的函数
 * @param {string} chatContext.currentChatId - 当前聊天ID
 * @returns {Promise<void>}
 */
export async function displayTypingResponse(responseText, chatContext) {
  console.log('开始显示打字机效果:', responseText.substring(0, 50) + '...')

  // 添加AI消息占位符
  const aiMessageId = Date.now().toString()
  const aiMsg = {
    id: aiMessageId,
    role: 'assistant',
    content: '',
    isTyping: true
  }

  chatContext.addMessage(aiMsg)
  console.log('添加AI消息占位符:', aiMsg)

  // 滚动到底部
  if (chatContext.scrollToBottom) {
    nextTick(() => {
      chatContext.scrollToBottom()
    })
  }

  // 模拟打字机效果
  let currentText = ''

  for (let i = 0; i < responseText.length; i++) {
    currentText += responseText[i]

    // 更新消息内容
    chatContext.updateMessage(aiMessageId, { content: currentText })

    // 根据字符类型调整速度，模拟真实阅读节奏
    let delay = typingConfig.baseSpeed
    const char = responseText[i]

    if (char === '\n') {
      delay = typingConfig.newlineDelay
    } else if (char === '。' || char === '！' || char === '？' || char === '.' || char === '!' || char === '?') {
      delay = typingConfig.periodDelay
    } else if (char === '，' || char === '；' || char === '：' || char === ',' || char === ';' || char === ':') {
      delay = typingConfig.commaDelay
    } else if (char === ' ') {
      delay = typingConfig.spaceSpeed
    } else if (char === '*' || char === '#' || char === '-' || char === '`' || char === '(' || char === ')' || char === '[' || char === ']') {
      delay = typingConfig.markdownSpeed
    } else if (/[a-zA-Z0-9]/.test(char)) {
      delay = typingConfig.englishSpeed
    } else if (/[\u4e00-\u9fa5]/.test(char)) {
      delay = typingConfig.chineseSpeed
    }

    // 添加随机变化，让打字更自然（±20%的随机变化）
    const randomFactor = 0.8 + Math.random() * 0.4 // 0.8 到 1.2 之间
    delay = Math.round(delay * randomFactor)

    await new Promise(resolve => setTimeout(resolve, delay))

    // 智能滚动：在换行或标点符号后滚动，或者每8个字符滚动一次
    if (char === '\n' || char === '。' || char === '！' || char === '？' || char === '.' || char === '!' || char === '?' || i % 8 === 0) {
      if (chatContext.scrollToBottom) {
        nextTick(() => {
          chatContext.scrollToBottom()
        })
      }
    }
  }

  // 完成打字效果
  chatContext.updateMessage(aiMessageId, { isTyping: false })
  console.log('打字机效果完成')

  // 保存消息到本地存储
  if (chatContext.saveMessages && chatContext.currentChatId) {
    chatContext.saveMessages()
  }

  // 最终滚动到底部
  if (chatContext.scrollToBottom) {
    nextTick(() => {
      chatContext.scrollToBottom()
    })
  }

  // 打字完成后稍微停顿一下，让用户有时间阅读
  await new Promise(resolve => setTimeout(resolve, 500))
}

/**
 * 处理关键词匹配回复
 * @param {string} userMessage - 用户消息
 * @param {Object} keywordConfig - 关键词配置
 * @param {Object} chatContext - 聊天上下文对象
 * @param {Function} chatContext.addMessage - 添加消息的函数
 * @param {Function} chatContext.updateMessage - 更新消息的函数
 * @param {Function} chatContext.saveMessages - 保存消息的函数
 * @param {Function} chatContext.scrollToBottom - 滚动到底部的函数
 * @param {Function} chatContext.clearInput - 清空输入框的函数
 * @param {Function} chatContext.callAIForProcessing - 调用AI处理的函数（可选）
 * @param {string} chatContext.currentChatId - 当前聊天ID
 * @returns {Promise<void>}
 */
export async function handleKeywordResponse(userMessage, keywordConfig, chatContext) {
  console.log('处理关键词匹配回复:', { userMessage, keywordConfig })

  // 添加用户消息（普通消息，不是打字机效果）
  const userMsg = {
    id: Date.now().toString(),
    role: 'user',
    content: userMessage
  }

  chatContext.addMessage(userMsg)
  console.log('添加用户消息:', userMsg)

  // 清空输入框
  if (chatContext.clearInput) {
    chatContext.clearInput()
  }

  // 保存用户消息到本地存储
  if (chatContext.saveMessages) {
    chatContext.saveMessages()
  }

  // 滚动到底部显示用户消息
  if (chatContext.scrollToBottom) {
    nextTick(() => {
      chatContext.scrollToBottom()
    })
  }

  // 等待一小段时间确保用户消息显示
  await new Promise(resolve => setTimeout(resolve, 100))

  // 如果需要AI处理，先显示固定回复，然后调用AI接口
  if (keywordConfig.needsAIProcessing) {
    console.log('需要AI处理，先显示固定回复')
    // 先显示固定格式回复（带打字机效果）
    await displayTypingResponse(keywordConfig.response, chatContext)

    // 然后调用AI接口进行进一步处理
    if (chatContext.callAIForProcessing) {
      await chatContext.callAIForProcessing(userMessage, keywordConfig.response)
    }
  } else {
    console.log('直接显示固定回复')
    // 直接显示固定回复（带打字机效果）
    await displayTypingResponse(keywordConfig.response, chatContext)
  }
}

/**
 * 处理处置消息
 * @param {string} message - 处置消息
 * @param {Object} chatContext - 聊天上下文对象
 * @param {Function} chatContext.addMessage - 添加消息的函数
 * @param {Function} chatContext.saveMessages - 保存消息的函数
 * @param {Function} chatContext.scrollToBottom - 滚动到底部的函数
 * @param {Function} chatContext.clearInput - 清空输入框的函数
 * @returns {void}
 */
export function handleDisposalMessage(message, chatContext) {
  // 添加用户消息
  const userMsg = {
    id: Date.now().toString(),
    role: 'user',
    content: message
  }
  chatContext.addMessage(userMsg)

  // 立即添加处置卡片消息
  const disposalMsg = {
    id: (Date.now() + 1).toString(),
    role: 'assistant',
    type: 'disposal',
    originalMessage: message,
    content: '安全处置任务已生成，请确认执行'
  }
  chatContext.addMessage(disposalMsg)

  // 清空输入框
  if (chatContext.clearInput) {
    chatContext.clearInput()
  }

  // 保存消息
  if (chatContext.saveMessages) {
    chatContext.saveMessages()
  }

  // 滚动到底部
  if (chatContext.scrollToBottom) {
    nextTick(() => {
      chatContext.scrollToBottom()
    })
  }
}

/**
 * 处理消息的主要函数
 * 这是外部调用的主要接口，包含完整的消息处理逻辑
 * @param {string} message - 用户输入的消息
 * @param {Object} chatContext - 聊天上下文对象
 * @param {Function} chatContext.addMessage - 添加消息的函数
 * @param {Function} chatContext.updateMessage - 更新消息的函数
 * @param {Function} chatContext.saveMessages - 保存消息的函数
 * @param {Function} chatContext.scrollToBottom - 滚动到底部的函数
 * @param {Function} chatContext.clearInput - 清空输入框的函数
 * @param {Function} chatContext.callAIForProcessing - 调用AI处理的函数（可选）
 * @param {string} chatContext.currentChatId - 当前聊天ID
 * @returns {Promise<boolean>} 是否处理了消息（true表示已处理，false表示需要继续正常流程）
 */
export async function handleMessage(message, chatContext) {
  // 检查功能是否启用
  if (!isKeywordMatchingEnabled()) {
    return false
  }

  const keywordResult = processKeywordMessage(message)
  if (keywordResult) {
    if (keywordResult.type === 'disposal') {
      handleDisposalMessage(message, chatContext)
      return true
    } else if (keywordResult.type === 'keyword') {
      await handleKeywordResponse(message, keywordResult.config, chatContext)
      return true
    }
  }

  return false
}

/**
 * 获取打字机配置
 * @returns {Object} 打字机配置对象
 */
export function getTypingConfig() {
  return { ...typingConfig }
}

/**
 * 更新打字机配置
 * @param {Object} newConfig - 新的配置
 */
export function updateTypingConfig(newConfig) {
  Object.assign(typingConfig, newConfig)
}

/**
 * 重置打字机配置为默认值
 */
export function resetTypingConfig() {
  Object.assign(typingConfig, {
    baseSpeed: 150,
    chineseSpeed: 100,
    englishSpeed: 45,
    spaceSpeed: 40,
    markdownSpeed: 30,
    commaDelay: 300,
    periodDelay: 400,
    newlineDelay: 1050
  })
}
