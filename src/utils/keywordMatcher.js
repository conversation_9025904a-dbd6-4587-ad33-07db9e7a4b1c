/**
 * 关键词匹配模块
 * 用于处理特定关键词的自动回复功能，包括打字机效果、消息处理等
 */

// import { nextTick } from 'vue'
// 在非 Vue 环境中使用 Promise.resolve().then() 模拟 nextTick
const nextTick = (callback) => {
  if (typeof callback === 'function') {
    return Promise.resolve().then(callback)
  }
  return Promise.resolve()
}

// 打字机速度配置
const typingConfig = {
  baseSpeed: 150,        // 基础速度（毫秒）
  chineseSpeed: 100,     // 中文字符速度
  englishSpeed: 45,      // 英文数字速度
  spaceSpeed: 40,        // 空格速度
  markdownSpeed: 30,     // Markdown符号速度
  commaDelay: 300,       // 逗号停顿
  periodDelay: 400,      // 句号停顿
  newlineDelay: 1050     // 换行停顿
}

// 关键词匹配配置
const keywordResponses = [
  {
    showResponse: false,
    needsAIProcessing: true, // 标记需要AI进一步处理
    keywords: ['4A系统所有欧拉操作系统的主机和数据库主机要做升级改造'],
    response: `您好！已收到您关于7月31日4A系统欧拉操作系统及数据库主机升级改造的网络安全“三同步”（同步规划、同步建设、同步运行）需求。经查询资产库，4A系统升级涉及资产IP清单如下：
《资产清单》
请确认资产清单是否准确？
用户：准确的，请帮我于凌晨4:30进行网络安全三同步操作`
  },
  {
    showResponse: false,
    needsAIProcessing: true, // 标记需要AI进一步处理
    keywords: ['根据《中国移动网络安全三同步管理办法》要求'],
    response: `1.渗透测试
    时间节点：2025年7月31日4:30
    执行内容：邮件通知安服部门进行渗透测试，并输出报告。此步骤旨在验证系统在攻击情况下的安全性，确保所有潜在漏洞被识别并记录用。

    2.漏洞扫描
    时间节点：2025年7月31日4:32
    执行内容：调用绿盟扫描器对资产进行漏扫并输出报告。如有风险资产，需通过EOMS派发工单处理。此步骤确保系统在漏洞层面的安全性。

    3.安全设备有效性验证
    时间节点：2025年7月31日4:35
    执行内容：调用BAS对资产进行有效性验证并输出报告，以确认安全设备的正常运行状态。此步骤确保所有安全措施在实际运行中的有效性和稳定性“[引用]。

    请确认是否要执行以上计划？`
  },
  {
    showResponse: false,
    needsAIProcessing: true, // 标记需要AI进一步处理
    isShowScriptModal: true,
    keywords: ['请帮我执行计划', '并将汇总结果发我'],
    response: `好的，根据您的需求正在执行计划1、2、3
    1.已为您启动渗透测试剧本，任务名为【AI_渗透测试_0731_001】,任务ID为【591】。接下来您可以继续监控该任务的进度和完成后输出的渗透测试报告。
    （弹出剧本执行框）
    2.已为您启动漏洞扫描剧本，任务名为【AI_漏洞扫描_0731_006】,任务ID为【1432】。接下来您可以继续监控该任务的进度和完成后输出的漏扫报告报告。
    （弹出剧本执行框）
    3.已为您启动安全设备有效性验证剧本，任务名为【AI_BAS_0731_003】,任务ID为【386】。接下来您可以继续监控该任务的进度和完成后输出的安全度量报告。`
  },
  {
    showResponse: false,
    needsAIProcessing: true, // 标记需要AI进一步处理
    keywords: ['查看以上3个剧本任务是否'],
    response: `您发起的3个安全验证剧本当前执行进度如下：
      渗透测试剧本_[AI_渗透测试_0731_001 | ID:591]
      ✅ 进度: 58%
      漏洞扫描剧本_[AI_漏洞扫描_0731_006 | ID:1432]
      ✅ 进度: 23%
      BAS验证剧本_[AI_BAS_0731_003 | ID:386]
      ✅ 进度: 71%`
  },
  {
    showResponse: false,
    needsAIProcessing: true, // 标记需要AI进一步处理
    keywords: ['再次查询3个剧本进度'],
    response: `好的，您发起的3个安全验证剧本当前执行进度如下：
      渗透测试剧本_[AI_渗透测试_0731_001 | ID:591]
      ✅ 进度: 100%
      漏洞扫描剧本_[AI_漏洞扫描_0731_006 | ID:1432]
      ✅ 进度:100%
      ✅ 关键发现:高危漏洞2个
      ✅ 关联工单: AH-074-250731-12（已自动派发EOMS工单）
      BAS验证剧本_[AI_BAS_0731_003 | ID:386]
      ✅ 进度:100%

      是否需要更详细的内容？`
  },
  {
    showResponse: false,
    needsAIProcessing: true, // 标记需要AI进一步处理
    keywords: ['告知我整体结果'],
    response: `本次安全验证漏洞扫描共发现1个风险资产，2个高危漏洞，已自动派发EOMS工单进行整改，工单号为AH-074-250731-12。渗透测试、BAS验证均为正常。`
  },
  // 第二个场景
  {
    showResponse: false,
    needsAIProcessing: true, // 标记需要AI进一步处理
    keywords: ['态势感知平台监测到勒索病毒告警', '原始告警日志如下'],
    response: `**【自动化威胁简报】**

**事件等级**: **危急**（CVSS9.8|影响核心财务服务器）

** 攻击摘要**:
- **病毒类型**: LockBit变种（通过CVE-2025-32745入侵）
- **加密范围**: 2,847个财务文件（扩展名已变更为.xls.encrypted）
- **横向移动**: 检测到Pass-the-Hash攻击尝试（目标:*************-50）

** 关键处置建议**:

**1. 立即隔离**：
- 阻断主机SRV-FINANCE-01所有网络连接
- 禁用账户dev_ops和admin（检测到异常令牌使用）

**2. 取证留存**：
- 内存转储（已检测CobaltStrike痕迹）
- 保留加密样本SHA-256

**3. 恢复路径**:
- 从备份BK_20240804_0200优先恢复C:\\Financial_Data\\

**是否执行以下操作？**
1. **隔离主机**
2. **禁用账户**
3. **启动备份恢复**
4. **向上级发送告警摘要**（含比特币地址和C2IP）
5. **修补漏洞**CVE-2025-32745和CVE-2025-11233`
  },

  {
    showResponse: false,
    needsAIProcessing: true, // 标记需要AI进一步处理
    keywords: ['确认执行所有操作'],
    response: `**⚡ 实时执行状态**

** 1. 隔离完成**
- 主机SRV-FINANCE-01已断网隔离

** 2. 账号禁用已完成**
- dev_ops账户已禁用
- admin账户已禁用

** 3. 备份恢复进行中**
- 正在从BK_20240804_0200恢复数据...
- 预计完成时间：30分钟

** 4. 工单已派发**
- 工单号：SEC-2025-0731-001

** 5. 修补漏洞邮件已发送**
- 已通知系统管理员进行漏洞修补`
  },
]

/**
 * 检测消息是否匹配关键词
 * @param {string} message - 用户输入的消息
 * @returns {Object|null} 匹配的配置对象，如果没有匹配则返回 null
 */
export function matchKeywords(message) {
  if (!message || typeof message !== 'string') {
    return null
  }

  for (const config of keywordResponses) {
    const hasMatch = config.keywords.some(keyword =>
      message.includes(keyword)
    )
    if (hasMatch) {
      return config
    }
  }
  return null
}

/**
 * 检测是否为处置消息
 * @param {string} message - 用户输入的消息
 * @returns {boolean} 是否为处置消息
 */
export function isDisposalMessage(message) {
  if (!message || typeof message !== 'string') {
    return false
  }
  return message.startsWith('处置：')
}

/**
 * 获取所有关键词配置（用于调试或管理）
 * @returns {Array} 关键词配置数组
 */
export function getKeywordConfigs() {
  return [...keywordResponses] // 返回副本，避免外部修改
}

/**
 * 添加新的关键词配置
 * @param {Object} config - 新的关键词配置
 * @param {Array<string>} config.keywords - 关键词数组
 * @param {string} config.response - 回复内容
 * @param {boolean} [config.needsAIProcessing] - 是否需要AI进一步处理
 * @param {boolean} [config.showResponse] - 是否显示预设回复内容，false时直接调用AI处理
 * @param {boolean} [config.isShowScriptModal] - 是否在AI输出完成后显示脚本执行卡片
 */
export function addKeywordConfig(config) {
  if (!config || !config.keywords || !config.response) {
    throw new Error('关键词配置格式错误')
  }
  
  if (!Array.isArray(config.keywords) || config.keywords.length === 0) {
    throw new Error('关键词必须是非空数组')
  }
  
  if (typeof config.response !== 'string' || config.response.trim() === '') {
    throw new Error('回复内容不能为空')
  }
  
  keywordResponses.push({
    keywords: [...config.keywords], // 创建副本
    response: config.response,
    needsAIProcessing: Boolean(config.needsAIProcessing),
    showResponse: config.showResponse !== false, // 默认为true，只有明确设置为false时才为false
    isShowScriptModal: Boolean(config.isShowScriptModal) // 是否显示脚本执行卡片
  })
}

/**
 * 移除关键词配置
 * @param {number} index - 要移除的配置索引
 */
export function removeKeywordConfig(index) {
  if (index >= 0 && index < keywordResponses.length) {
    keywordResponses.splice(index, 1)
  }
}

/**
 * 清空所有关键词配置
 */
export function clearKeywordConfigs() {
  keywordResponses.length = 0
}

/**
 * 检查是否启用关键词匹配功能
 * 可以通过环境变量或配置文件控制
 * @returns {boolean} 是否启用关键词匹配
 */
export function isKeywordMatchingEnabled() {
  // 可以通过环境变量控制
  if (typeof window !== 'undefined' && window.DISABLE_KEYWORD_MATCHING) {
    return false
  }
  
  // 默认启用
  return true
}

/**
 * 主要的关键词处理函数
 * 这是外部调用的主要接口
 * @param {string} message - 用户输入的消息
 * @returns {Object|null} 处理结果
 */
export function processKeywordMessage(message) {
  // 检查功能是否启用
  if (!isKeywordMatchingEnabled()) {
    return null
  }

  // 检查是否为处置消息
  if (isDisposalMessage(message)) {
    return {
      type: 'disposal',
      message: message
    }
  }

  // 检查关键词匹配
  const keywordMatch = matchKeywords(message)
  if (keywordMatch) {
    return {
      type: 'keyword',
      config: keywordMatch,
      message: message
    }
  }

  return null
}

/**
 * 格式化打字内容（保持换行和格式）
 * @param {string} content - 要格式化的内容
 * @returns {string} 格式化后的HTML内容
 */
export function formatTypingContent(content) {
  if (!content) return ''

  return content
    // 处理换行
    .replace(/\n/g, '<br>')
    // 处理加粗文本 **text**
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    // 处理斜体文本 *text*
    .replace(/\*(.*?)\*/g, '<em>$1</em>')
    // 处理代码 `code`
    .replace(/`(.*?)`/g, '<code style="background-color: #f5f5f5; padding: 2px 4px; border-radius: 3px; font-family: monospace;">$1</code>')
    // 处理emoji和特殊符号，保持原样
    .replace(/(🔍|🛡️|✅|📋|📊|🚨|⚠️|🎯|⚡|🔄|📧)/g, '<span style="font-size: 1.1em;">$1</span>')
    // 处理列表项（以 - 开头的行）
    .replace(/^- (.*?)$/gm, '<div style="margin-left: 16px; margin-bottom: 4px;">• $1</div>')
    // 处理数字列表（以数字开头的行）
    .replace(/^(\d+)\. (.*?)$/gm, '<div style="margin-left: 16px; margin-bottom: 4px;">$1. $2</div>')
}

/**
 * 显示打字机效果的回复
 * @param {string} responseText - 回复文本
 * @param {Object} chatContext - 聊天上下文对象
 * @param {Function} chatContext.addMessage - 添加消息的函数
 * @param {Function} chatContext.updateMessage - 更新消息的函数
 * @param {Function} chatContext.saveMessages - 保存消息的函数
 * @param {Function} chatContext.scrollToBottom - 滚动到底部的函数
 * @param {string} chatContext.currentChatId - 当前聊天ID
 * @returns {Promise<void>}
 */
export async function displayTypingResponse(responseText, chatContext) {
  console.log('开始显示打字机效果:', responseText.substring(0, 50) + '...')

  // 添加AI消息占位符
  const aiMessageId = Date.now().toString()
  const aiMsg = {
    id: aiMessageId,
    role: 'assistant',
    content: '',
    isTyping: true,
    formattedContent: '' // 添加格式化内容字段
  }

  chatContext.addMessage(aiMsg)
  console.log('添加AI消息占位符:', aiMsg)

  // 滚动到底部
  if (chatContext.scrollToBottom) {
    nextTick(() => {
      chatContext.scrollToBottom()
    })
  }

  // 添加1-5秒的随机思考时间
  const thinkingTime = Math.floor(Math.random() * 4000) + 1000 // 1000-5000毫秒
  console.log(`AI思考中，等待 ${thinkingTime}ms...`)

  // 显示思考状态
  chatContext.updateMessage(aiMessageId, {
    content: '正在分析中...',
    formattedContent: '正在分析中...'
  })

  await new Promise(resolve => setTimeout(resolve, thinkingTime))

  // 清空思考状态，开始打字
  chatContext.updateMessage(aiMessageId, {
    content: '',
    formattedContent: ''
  })

  // 模拟打字机效果
  let currentText = ''
  let currentFormattedText = ''

  for (let i = 0; i < responseText.length; i++) {
    currentText += responseText[i]

    // 实时格式化内容
    currentFormattedText = formatTypingContent(currentText)

    // 更新消息内容（同时更新原始内容和格式化内容）
    chatContext.updateMessage(aiMessageId, {
      content: currentText,
      formattedContent: currentFormattedText
    })

    // 根据字符类型调整速度，模拟真实阅读节奏
    let delay = typingConfig.baseSpeed
    const char = responseText[i]

    if (char === '\n') {
      delay = typingConfig.newlineDelay
    } else if (char === '。' || char === '！' || char === '？' || char === '.' || char === '!' || char === '?') {
      delay = typingConfig.periodDelay
    } else if (char === '，' || char === '；' || char === '：' || char === ',' || char === ';' || char === ':') {
      delay = typingConfig.commaDelay
    } else if (char === ' ') {
      delay = typingConfig.spaceSpeed
    } else if (char === '*' || char === '#' || char === '-' || char === '`' || char === '(' || char === ')' || char === '[' || char === ']') {
      delay = typingConfig.markdownSpeed
    } else if (/[a-zA-Z0-9]/.test(char)) {
      delay = typingConfig.englishSpeed
    } else if (/[\u4e00-\u9fa5]/.test(char)) {
      delay = typingConfig.chineseSpeed
    }

    // 添加随机变化，让打字更自然（±20%的随机变化）
    const randomFactor = 0.8 + Math.random() * 0.4 // 0.8 到 1.2 之间
    delay = Math.round(delay * randomFactor)

    await new Promise(resolve => setTimeout(resolve, delay))

    // 智能滚动：在换行或标点符号后滚动，或者每8个字符滚动一次
    if (char === '\n' || char === '。' || char === '！' || char === '？' || char === '.' || char === '!' || char === '?' || i % 8 === 0) {
      if (chatContext.scrollToBottom) {
        nextTick(() => {
          chatContext.scrollToBottom()
        })
      }
    }
  }

  // 完成打字效果，保持格式化样式
  const finalFormattedContent = formatTypingContent(responseText)
  chatContext.updateMessage(aiMessageId, {
    isTyping: false,
    content: responseText,
    formattedContent: finalFormattedContent
  })

  // 保存消息到本地存储
  if (chatContext.saveMessages && chatContext.currentChatId) {
    chatContext.saveMessages()
  }

  // 最终滚动到底部
  if (chatContext.scrollToBottom) {
    nextTick(() => {
      chatContext.scrollToBottom()
    })
  }

  // 打字完成后稍微停顿一下，让用户有时间阅读
  await new Promise(resolve => setTimeout(resolve, 500))

  // 返回消息ID，用于后续可能的脚本卡片添加
  return aiMessageId
}

/**
 * 处理关键词匹配回复
 * @param {string} userMessage - 用户消息
 * @param {Object} keywordConfig - 关键词配置
 * @param {Array<string>} keywordConfig.keywords - 关键词数组
 * @param {string} keywordConfig.response - 回复内容
 * @param {boolean} [keywordConfig.needsAIProcessing] - 是否需要AI进一步处理
 * @param {boolean} [keywordConfig.showResponse] - 是否显示预设回复内容，false时直接调用AI处理
 * @param {Object} chatContext - 聊天上下文对象
 * @param {Function} chatContext.addMessage - 添加消息的函数
 * @param {Function} chatContext.updateMessage - 更新消息的函数
 * @param {Function} chatContext.saveMessages - 保存消息的函数
 * @param {Function} chatContext.scrollToBottom - 滚动到底部的函数
 * @param {Function} chatContext.clearInput - 清空输入框的函数
 * @param {Function} chatContext.callAIForProcessing - 调用AI处理的函数（可选）
 * @param {string} chatContext.currentChatId - 当前聊天ID
 * @returns {Promise<void>}
 */
export async function handleKeywordResponse(userMessage, keywordConfig, chatContext) {

  // 添加用户消息（普通消息，不是打字机效果）
  const userMsg = {
    id: Date.now().toString(),
    role: 'user',
    content: userMessage
  }

  chatContext.addMessage(userMsg)

  // 清空输入框
  if (chatContext.clearInput) {
    chatContext.clearInput()
  }

  // 保存用户消息到本地存储
  if (chatContext.saveMessages) {
    chatContext.saveMessages()
  }

  // 滚动到底部显示用户消息
  if (chatContext.scrollToBottom) {
    nextTick(() => {
      chatContext.scrollToBottom()
    })
  }

  // 等待一小段时间确保用户消息显示
  await new Promise(resolve => setTimeout(resolve, 100))

  // 检查是否有 showResponse 属性
  if (keywordConfig.showResponse === false) {
    console.log('跳过显示预设回复，直接调用AI处理')
    // 不显示预设回复，直接调用AI接口，将response作为提示词
    if (chatContext.callAIForProcessing) {
      const aiMessageId = await chatContext.callAIForProcessing(userMessage, keywordConfig.response)

      // 检查是否需要显示脚本执行卡片
      if (keywordConfig.isShowScriptModal && aiMessageId) {
        await addScriptExecutionCard(keywordConfig, userMessage, chatContext, aiMessageId)
      }
    } else {
      console.warn('callAIForProcessing 方法未定义，无法处理AI请求')
    }
  } else if (keywordConfig.needsAIProcessing) {
    console.log('需要AI处理，先显示固定回复')
    // 先显示固定格式回复（带打字机效果）
    const aiMessageId = await displayTypingResponse(keywordConfig.response, chatContext)

    // 然后调用AI接口进行进一步处理
    if (chatContext.callAIForProcessing) {
      await chatContext.callAIForProcessing(userMessage, keywordConfig.response)
    }

    // 检查是否需要显示脚本执行卡片
    if (keywordConfig.isShowScriptModal && aiMessageId) {
      await addScriptExecutionCard(keywordConfig, userMessage, chatContext, aiMessageId)
    }
  } else {
    console.log('直接显示固定回复')
    // 直接显示固定回复（带打字机效果）
    const aiMessageId = await displayTypingResponse(keywordConfig.response, chatContext)

    // 检查是否需要显示脚本执行卡片
    if (keywordConfig.isShowScriptModal && aiMessageId) {
      await addScriptExecutionCard(keywordConfig, userMessage, chatContext, aiMessageId)
    }
  }
}

/**
 * 添加脚本执行卡片
 * @param {Object} keywordConfig - 关键词配置
 * @param {string} userMessage - 用户消息
 * @param {Object} chatContext - 聊天上下文对象
 * @param {string} aiMessageId - AI消息ID
 * @returns {Promise<void>}
 */
async function addScriptExecutionCard(keywordConfig, userMessage, chatContext, aiMessageId) {
  console.log('添加脚本执行卡片')

  // 等待一小段时间，确保AI回复完全显示
  await new Promise(resolve => setTimeout(resolve, 1000))

  // 创建脚本执行卡片消息
  const scriptCardId = Date.now().toString()
  const scriptCardMsg = {
    id: scriptCardId,
    role: 'assistant',
    type: 'script-execution',
    taskData: {
      summary: generateTaskSummary(userMessage, keywordConfig),
      executionPlan: keywordConfig.response,
      response: keywordConfig.response,
      taskType: detectTaskType(userMessage),
      relatedMessageId: aiMessageId
    },
    userMessage: userMessage,
    content: '脚本执行任务已创建，请查看详细信息'
  }

  // 添加脚本卡片消息
  chatContext.addMessage(scriptCardMsg)

  // 保存消息
  if (chatContext.saveMessages) {
    chatContext.saveMessages()
  }

  // 滚动到底部
  if (chatContext.scrollToBottom) {
    nextTick(() => {
      chatContext.scrollToBottom()
    })
  }
}

/**
 * 生成任务摘要
 * @param {string} userMessage - 用户消息
 * @param {Object} keywordConfig - 关键词配置
 * @returns {string} 任务摘要
 */
function generateTaskSummary(userMessage, keywordConfig) {
  if (userMessage.includes('4A系统') && userMessage.includes('欧拉操作系统')) {
    return '执行4A系统欧拉操作系统升级改造的网络安全三同步检查，包括渗透测试、漏洞扫描、安全设备有效性验证等操作。'
  } else if (userMessage.includes('三同步')) {
    return '执行网络安全三同步脚本，包括渗透测试、漏洞扫描、安全设备有效性验证等操作。'
  } else if (userMessage.includes('勒索病毒')) {
    return '执行勒索病毒应急响应脚本，包括病毒隔离、系统扫描、安全加固等操作。'
  } else {
    return '执行安全运维脚本任务，根据具体需求进行相应的安全检查和处置操作。'
  }
}

/**
 * 检测任务类型
 * @param {string} userMessage - 用户消息
 * @returns {string} 任务类型
 */
function detectTaskType(userMessage) {
  if (userMessage.includes('4A系统') || userMessage.includes('三同步')) {
    return 'security-sync'
  } else if (userMessage.includes('勒索病毒')) {
    return 'ransomware-response'
  } else if (userMessage.includes('漏洞')) {
    return 'vulnerability-scan'
  } else {
    return 'general-security'
  }
}

/**
 * 处理处置消息
 * @param {string} message - 处置消息
 * @param {Object} chatContext - 聊天上下文对象
 * @param {Function} chatContext.addMessage - 添加消息的函数
 * @param {Function} chatContext.saveMessages - 保存消息的函数
 * @param {Function} chatContext.scrollToBottom - 滚动到底部的函数
 * @param {Function} chatContext.clearInput - 清空输入框的函数
 * @returns {void}
 */
export function handleDisposalMessage(message, chatContext) {
  // 添加用户消息
  const userMsg = {
    id: Date.now().toString(),
    role: 'user',
    content: message
  }
  chatContext.addMessage(userMsg)

  // 立即添加处置卡片消息
  const disposalMsg = {
    id: (Date.now() + 1).toString(),
    role: 'assistant',
    type: 'disposal',
    originalMessage: message,
    content: '安全处置任务已生成，请确认执行'
  }
  chatContext.addMessage(disposalMsg)

  // 清空输入框
  if (chatContext.clearInput) {
    chatContext.clearInput()
  }

  // 保存消息
  if (chatContext.saveMessages) {
    chatContext.saveMessages()
  }

  // 滚动到底部
  if (chatContext.scrollToBottom) {
    nextTick(() => {
      chatContext.scrollToBottom()
    })
  }
}

/**
 * 处理消息的主要函数
 * 这是外部调用的主要接口，包含完整的消息处理逻辑
 * @param {string} message - 用户输入的消息
 * @param {Object} chatContext - 聊天上下文对象
 * @param {Function} chatContext.addMessage - 添加消息的函数
 * @param {Function} chatContext.updateMessage - 更新消息的函数
 * @param {Function} chatContext.saveMessages - 保存消息的函数
 * @param {Function} chatContext.scrollToBottom - 滚动到底部的函数
 * @param {Function} chatContext.clearInput - 清空输入框的函数
 * @param {Function} chatContext.callAIForProcessing - 调用AI处理的函数（可选）
 * @param {string} chatContext.currentChatId - 当前聊天ID
 * @returns {Promise<boolean>} 是否处理了消息（true表示已处理，false表示需要继续正常流程）
 */
export async function handleMessage(message, chatContext) {
  // 检查功能是否启用
  if (!isKeywordMatchingEnabled()) {
    return false
  }

  const keywordResult = processKeywordMessage(message)
  if (keywordResult) {
    if (keywordResult.type === 'disposal') {
      handleDisposalMessage(message, chatContext)
      return true
    } else if (keywordResult.type === 'keyword') {
      await handleKeywordResponse(message, keywordResult.config, chatContext)
      return true
    }
  }

  return false
}

/**
 * 获取打字机配置
 * @returns {Object} 打字机配置对象
 */
export function getTypingConfig() {
  return { ...typingConfig }
}

/**
 * 更新打字机配置
 * @param {Object} newConfig - 新的配置
 */
export function updateTypingConfig(newConfig) {
  Object.assign(typingConfig, newConfig)
}

/**
 * 重置打字机配置为默认值
 */
export function resetTypingConfig() {
  Object.assign(typingConfig, {
    baseSpeed: 150,
    chineseSpeed: 100,
    englishSpeed: 45,
    spaceSpeed: 40,
    markdownSpeed: 30,
    commaDelay: 300,
    periodDelay: 400,
    newlineDelay: 1050
  })
}

/**
 * 使用示例：
 *
 * 1. 显示预设回复（默认行为）：
 * {
 *   keywords: ['关键词'],
 *   response: '预设回复内容',
 *   needsAIProcessing: false
 * }
 *
 * 2. 显示预设回复后再调用AI处理：
 * {
 *   keywords: ['关键词'],
 *   response: '预设回复内容',
 *   needsAIProcessing: true
 * }
 *
 * 3. 跳过预设回复，直接调用AI处理（新功能）：
 * {
 *   keywords: ['关键词'],
 *   response: '作为AI提示词的内容',
 *   showResponse: false,
 *   needsAIProcessing: true
 * }
 *
 * 4. 显示脚本执行卡片（新功能）：
 * {
 *   keywords: ['关键词'],
 *   response: '预设回复内容',
 *   needsAIProcessing: true,
 *   isShowScriptModal: true
 * }
 *
 * 在第3种情况下，response内容将作为提示词传递给AI模型，
 * 用户不会看到response的内容，只会看到AI加工后的输出。
 *
 * 在第4种情况下，AI输出完成后会显示一个脚本执行卡片，
 * 用户可以查看详细信息并执行相关的脚本任务。
 */
