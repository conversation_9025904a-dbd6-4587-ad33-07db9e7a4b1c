<template>
  <div class="chat-container">
    <!-- 左侧历史记录 -->
    <div class="chat-sidebar" :class="{ collapsed: isSidebarCollapsed }">
      <div class="sidebar-header">
        <n-button quaternary size="small" @click="router.back()">
          <template #icon>
            <SvgIcon icon="material-symbols:arrow-back" />
          </template>
          返回
        </n-button>
        <n-button class="new-chat-btn" @click="createNewChat">
          <template #icon>
            <SvgIcon icon="material-symbols:add" />
          </template>
          新对话
        </n-button>
      </div>
      
      <div class="chat-history">
        <div
          v-for="chat in chatHistory"
          :key="chat.id"
          class="history-item"
          :class="{ active: currentChatId === chat.id }"
          @click="switchChat(chat.id)"
        >
          <SvgIcon icon="material-symbols:chat" />
          <span class="truncate">{{ chat.sessionName || '新对话' }}</span>
          <n-button
            circle
            quaternary
            size="tiny"
            @click.stop="deleteChat(chat.id)"
          >
            <template #icon>
              <SvgIcon icon="material-symbols:delete" />
            </template>
          </n-button>
        </div>
      </div>
      
      <div class="sidebar-toggle" @click="toggleSidebar">
        <SvgIcon :icon="isSidebarCollapsed ? 'material-symbols:chevron-right' : 'material-symbols:chevron-left'" />
      </div>
    </div>

    <!-- 右侧聊天区域 -->
    <div class="chat-main">
      <div class="chat-header">
        <div class="header-content">
          <span class="chat-title">{{ knowledgeName || '新对话' }}</span>
        </div>
      </div>

      <!-- 消息列表 -->
      <div class="chat-messages" ref="messagesRef" @scroll="handleScroll">
        <template v-if="currentChat?.messages?.length">
          <div v-for="message in currentChat.messages" :key="message.id" class="message-item" :class="message.role">
            <div class="message-avatar">
              <SvgIcon :icon="message.role === 'user' ? 'material-symbols:person' : 'material-symbols:smart-toy'" />
            </div>
            <div class="message-content">
              <div class="message-bubble">
                <template v-if="message.role === 'user'">
                  <n-text class="message-text">
                    {{ message.content }}
                  </n-text>
                </template>
                <template v-else-if="message.type === 'disposal'">
                  <div class="disposal-message">
                    <DisposalCard
                      :original-message="message.originalMessage"
                      @execute-complete="handleDisposalComplete"
                    />
                  </div>
                </template>
                <template v-else-if="message.type === 'script-execution'">
                  <div class="script-execution-message">
                    <ScriptExecutionCard
                      :task-data="message.taskData"
                      :user-message="message.userMessage"
                      @execution-complete="handleScriptExecutionComplete"
                    />
                  </div>
                </template>
                <template v-else-if="message.isTyping">
                  <div class="typing-message">
                    <div class="typing-content" v-html="message.formattedContent || formatTypingContent(message.content)"></div>
                  </div>
                </template>
                <template v-else>
                  <!-- 如果有格式化内容，优先显示格式化内容 -->
                  <div v-if="message.formattedContent" class="formatted-message" v-html="message.formattedContent"></div>
                  <HybridRenderer v-else :data="message.content" :stream-data="message.streamData" />
                </template>
              </div>
              <div class="message-actions">
                <n-button
                  v-if="message.role === 'assistant'"
                  quaternary
                  size="tiny"
                  @click="copyToClipboard(message.content)"
                >
                  <template #icon>
                    <SvgIcon icon="material-symbols:content-copy" />
                  </template>
                  复制
                </n-button>
              </div>
            </div>
          </div>
        </template>
        
        <template v-else>
          <div class="empty-state">
            <p>开始一个新的对话吧</p>
          </div>
        </template>

        <!-- 加载中状态 -->
        <div v-if="loading && isStreamIng" class="message-item assistant">
          <div class="message-avatar">
            <SvgIcon icon="material-symbols:smart-toy" />
          </div>
          <div class="message-content">
            <div class="message-bubble loading">
              <HybridRenderer :data="markdownContent" :stream-data="streamData" />
            </div>
          </div>
        </div>
      </div>

      <!-- 输入区域 -->
      <div v-if="showInput" class="chat-input-wrapper">
        <div class="chat-input">
          <n-input
            ref="inputRef"
            v-model:value="inputMessage"
            type="textarea"
            :autosize="{ minRows: 3, maxRows: 5 }"
            placeholder="输入问题..."
            @keydown="handleKeyDown"
          />
          <n-button 
            class="send-button" 
            :disabled="!inputMessage.trim() && !isStreamIng" 
            @click="isStreamIng ? handleStreamControl() : handleSend()"
          >
            <template #icon>
              <SvgIcon :icon="isStreamIng ? 'material-symbols:stop' : 'material-symbols:send'" />
            </template>
            {{ isStreamIng ? '结束' : '发送' }}
          </n-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, nextTick, watch, onUnmounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { NButton, NInput, NText } from 'naive-ui'
import SvgIcon from "@/components/common/SvgIcon/index.vue"
import api from './api'
import HybridRenderer from '@/components/hybridRenderer/index.vue'
import DisposalCard from '@/components/DisposalCard/index.vue'
import ScriptExecutionCard from '@/components/ScriptExecutionCard/index.vue'
import { handleMessage, formatTypingContent } from '@/utils/keywordMatcher'

defineOptions({ name: 'KnowledgeChat' })

const route = useRoute()
const router = useRouter()
const knowledgeId = route.params.id
const knowledgeName = route.query.name

const messagesRef = ref(null)
const isUserScrolling = ref(false) // 用户是否在手动滚动
const loading = ref(false)
const inputMessage = ref('')
const currentChatId = ref(null)
const chatHistory = ref([])
const inputRef = ref(null)
const isSidebarCollapsed = ref(false)

const markdownContent = ref('')
const streamData = ref(null) // 存储完整的流数据对象

const reader = ref(null)
const controller = ref(null)
const isStreamIng = ref(false)
const isStreamEnd = ref(false)
const streamBuffer = ref('') // 添加流缓冲区来处理分片数据

// 当前对话
const currentChat = computed(() => {
  return chatHistory.value.find(chat => chat.id === currentChatId.value)
})

// 添加一个计算属性来判断是否显示输入框
const showInput = computed(() => {
  return chatHistory.value.length > 0 && currentChat.value
})

// 加载会话列表
const loadChatList = async () => {
  const res = await api.getChatList({
    knowledgeId,
    page: 1,
    size: 999
  })
  chatHistory.value = res.data.rows || []
  
  // 加载本地存储的消息记录
  chatHistory.value = chatHistory.value.map(chat => {
    const localMessages = localStorage.getItem(`chat_messages_${chat.id}`)
    const messages = localMessages ? JSON.parse(localMessages) : []

    return {
      ...chat,
      messages
    }
  })

  if (chatHistory.value.length > 0) {
    // 有会话时选中第一个
    currentChatId.value = chatHistory.value[0].id
  } else {
    // 没有会话时创建新会话
    createNewChat()
  }
}

// 创建新对话
const createNewChat = async () => {
  const res = await api.creatChat({ 
    knowledgeId, 
    sessionName: '会话'+(chatHistory.value.length+1) 
  })
  // 将新创建的会话添加到列表开头
  const newChat = {
    ...res.data,
    messages: []
  }
  chatHistory.value.unshift(newChat)
  currentChatId.value = newChat.id
  // 保存空消息记录到本地
  saveMessages(newChat.id, [])
}

// 删除对话
const deleteChat = async (sessionId) => {
  const res = await api.deleteChat({ sessionId })
  const index = chatHistory.value.findIndex(chat => chat.id === sessionId)
  if (index > -1) {
    // 删除本地存储的消息记录
    localStorage.removeItem(`chat_messages_${sessionId}`)
    chatHistory.value.splice(index, 1)
    if (chatHistory.value.length === 0) {
      // 如果没有会话了，创建新会话
      createNewChat()
    } else {
      // 否则选中第一个会话
      currentChatId.value = chatHistory.value[0].id
    }
  }
}

// 保存消息记录到本地存储
const saveMessages = (sessionId, messages) => {
  try {
    // 确保数据完整性
    const messagesToSave = messages.map(msg => ({
      ...msg,
      // 确保streamData被正确保存
      streamData: msg.streamData || null
    }))

    const jsonString = JSON.stringify(messagesToSave)
    localStorage.setItem(`chat_messages_${sessionId}`, jsonString)

  } catch (error) {
    console.error('保存消息到localStorage失败:', error)
  }
}

// 处理流控制（结束流）
const handleStreamControl = () => {
  // 结束当前流输出
  stopStream()
}

// 停止流输出
const stopStream = () => {
  if (controller.value) {
    controller.value.abort()
  }
  if (reader.value) {
    reader.value.cancel()
  }

  // 重置所有流状态
  isStreamIng.value = false
  isStreamEnd.value = true
  loading.value = false
  reader.value = null
  controller.value = null

  // 如果有内容，保存到历史记录
  if (markdownContent.value.trim()) {
    const sessionId = currentChat.value?.id
    if (sessionId) {
      const newMessage = {
        id: Date.now().toString(),
        role: 'assistant',
        content: markdownContent.value,
        streamData: streamData.value
      }
      currentChat.value.messages.push(newMessage)
      saveMessages(sessionId, currentChat.value.messages)
    }
  }

  // 清理流式状态和内容
  markdownContent.value = ''
  streamData.value = null
}

// 切换对话
const switchChat = (chatId) => {
  // 如果当前正在进行对话，则不允许切换
  if (isStreamIng.value) {
    $message.warning('请等待当前回答完成')
    return
  }
  
  // 如果切换到当前对话，不做任何操作
  if (currentChatId.value === chatId) {
    return
  }

  // 重置所有状态
  inputMessage.value = ''
  loading.value = false
  isStreamIng.value = false
  isStreamEnd.value = false

  markdownContent.value = ''
  
  // 如果存在未完成的请求，取消它
  if (controller.value) {
    controller.value.abort()
    controller.value = null
  }
  if (reader.value) {
    reader.value = null
  }

  // 切换到新对话
  currentChatId.value = chatId
}

// 调用AI进行进一步处理
const callAIForProcessing = async (originalMessage, fixedResponse) => {
  try {
    loading.value = true
    isStreamIng.value = true
    isStreamEnd.value = false
    markdownContent.value = ''
    streamData.value = null
    streamBuffer.value = ''

    // 创建 AbortController 用于控制请求
    controller.value = new AbortController()

    // 构建包含固定回复的完整消息
    const enhancedMessage = `${originalMessage}\n\n以上是用户的问题，以下是固定格式的回复内容：\n${fixedResponse}\n\n请基于以上固定回复内容进行AI加工处理，保持核心内容不变，但可以优化表达方式和补充相关信息。`

    // 调用API获取AI处理后的回答
    reader.value = await api.chatStream({
      sessionId: currentChat.value.id,
      content: enhancedMessage
    })
    await processStream(currentChat.value.id)
  } catch (error) {
    console.error('AI处理失败:', error)
    loading.value = false
    isStreamIng.value = false
    isStreamEnd.value = true
    reader.value = null
    controller.value = null
  }
}

// 处理处置任务完成
const handleDisposalComplete = (result) => {
  console.log('处置任务完成:', result)

  if (!currentChat.value.messages) {
    currentChat.value.messages = []
  }

  // 添加执行结果消息
  currentChat.value.messages.push({
    id: Date.now().toString(),
    role: 'assistant',
    content: result.result,
    timestamp: new Date().toISOString()
  })

  // 滚动到底部
  nextTick(() => {
    scrollToBottom()
  })
}

// 处理脚本执行完成
const handleScriptExecutionComplete = (result) => {
  console.log('脚本执行完成:', result)

  if (!currentChat.value.messages) {
    currentChat.value.messages = []
  }

  // 添加执行结果消息
  currentChat.value.messages.push({
    id: Date.now().toString(),
    role: 'assistant',
    content: result.result,
    timestamp: new Date().toISOString(),
    taskId: result.taskId
  })

  // 保存消息
  if (currentChat.value?.id) {
    saveMessages(currentChat.value.id, currentChat.value.messages)
  }

  // 滚动到底部
  nextTick(() => {
    scrollToBottom()
  })
}

// 处理发送消息
const handleSend = async () => {
  const message = inputMessage.value.trim()
  if (!message || loading.value) return

  // 创建聊天上下文对象
  const chatContext = {
    addMessage: (msg) => {
      if (!currentChat.value.messages) {
        currentChat.value.messages = []
      }
      currentChat.value.messages.push(msg)
    },
    updateMessage: (messageId, updates) => {
      const message = currentChat.value.messages.find(msg => msg.id === messageId)
      if (message) {
        Object.assign(message, updates)
      }
    },
    saveMessages: () => {
      if (currentChat.value?.id) {
        saveMessages(currentChat.value.id, currentChat.value.messages)
      }
    },
    scrollToBottom: scrollToBottom,
    clearInput: () => {
      inputMessage.value = ''
    },
    callAIForProcessing: callAIForProcessing,
    currentChatId: currentChat.value?.id
  }

  // 使用关键词匹配模块处理消息
  const handled = await handleMessage(message, chatContext)
  if (handled) {
    return
  }

  const currentSessionId = currentChat.value.id

  // 添加用户消息
  if (!currentChat.value.messages) {
    currentChat.value.messages = []
  }
  currentChat.value.messages.push({
    id: Date.now().toString(),
    role: 'user',
    content: message
  })

  inputMessage.value = ''
  loading.value = true
  isStreamIng.value = true
  isStreamEnd.value = false
  markdownContent.value = '' // 重置流式输出内容
  streamData.value = null // 重置流数据
  streamBuffer.value = '' // 重置流缓冲区

  try {
    // 创建 AbortController 用于控制请求
    controller.value = new AbortController()

    // 调用API获取回答
    reader.value = await api.chatStream({
      sessionId: currentSessionId,
      content: message
    })
    await processStream(currentSessionId)
  } catch (error) {
    if (error.name === 'AbortError') {
    } else {
      console.error('获取回答失败:', error)
      console.error('获取回答失败')
    }
    // 发生错误时重置所有状态
    loading.value = false
    isStreamIng.value = false
    isStreamEnd.value = true

    reader.value = null
    controller.value = null
  }
}

// 处理流式输出
const processStream = async (sessionId) => {
  // 如果不是当前会话，直接返回
  if (sessionId !== currentChat.value?.id) {
    return
  }

  while (reader.value) {
    try {
      const { value, done } = await reader.value.read()
      
      if (done) {
        // 如果没有通过流结束标识处理，这里作为备用重置
        if (isStreamIng.value) {
          console.warn('流异常结束，执行备用状态重置')
          isStreamIng.value = false
          isStreamEnd.value = true

          loading.value = false
          reader.value = null
          controller.value = null
        }
        break
      }

      let decodeVal = new TextDecoder().decode(value)

      // 将新数据添加到缓冲区
      streamBuffer.value += decodeVal

      // 处理缓冲区中的完整数据行
      const lines = streamBuffer.value.split('\n')
      // 保留最后一行（可能不完整）
      streamBuffer.value = lines.pop() || ''

      // 处理每一行完整的数据
      for (const line of lines) {
        if (!line.trim()) continue

        try {
          const result = extractDataValues(line.trim())

          // 确保仍然是当前会话才更新内容
          if (result && sessionId === currentChat.value?.id) {
            // 如果是流结束标识，处理流结束逻辑
            if (result.isStreamEnd) {


              // 先保存最终消息
              if (sessionId === currentChat.value?.id) {


                const newMessage = {
                  id: Date.now().toString(),
                  role: 'assistant',
                  content: markdownContent.value,
                  streamData: streamData.value
                }
                currentChat.value.messages.push(newMessage)
                saveMessages(sessionId, currentChat.value.messages)
              }

              // 立即重置所有流状态
              isStreamIng.value = false
              isStreamEnd.value = true
              loading.value = false
              markdownContent.value = ''
              streamData.value = null
              reader.value = null
              controller.value = null
              return
            }

            // 只有当有有效answer时才更新内容（不能是空字符串）
            if (result.answer !== null && result.answer !== undefined && result.answer.trim() !== '') {
              markdownContent.value = result.answer
            }

            // 始终更新streamData，确保引用数据不丢失
            if (result.fullData) {
              // 合并数据，确保不丢失之前的引用数据
              streamData.value = {
                ...streamData.value,
                ...result.fullData,
                // 确保reference和doc_aggs数据被保留
                reference: result.fullData.reference || streamData.value?.reference || [],
                doc_aggs: result.fullData.doc_aggs || streamData.value?.doc_aggs || []
              }


            }
          }
        } catch (error) {
          console.error('解析单行流数据出错:', error, '数据:', line)
        }
      }
    } catch (error) {
      console.error('处理流式响应出错:', error)
      break
    }
  }
}

const extractDataValues = (jsonString) => {
  try {
    const cleanJson = jsonString.replace(/^data:/, '')
    const parsedData = JSON.parse(cleanJson)

    // 检查是否是流结束标识
    if (parsedData.code === 0 && parsedData.data === true) {
      return {
        answer: null, // 返回null表示这是结束标识，不需要更新内容
        fullData: null,
        isStreamEnd: true
      }
    }

    // 检查是否有有效的answer数据
    if (parsedData.data && typeof parsedData.data === 'object' && parsedData.data.answer !== undefined) {
      // 确保完整保存所有数据，包括reference和doc_aggs
      const fullData = {
        ...parsedData.data,
        // 确保reference数据被保存
        reference: parsedData.data.reference || [],
        // 确保doc_aggs数据被保存
        doc_aggs: parsedData.data.doc_aggs || []
      }


      return {
        answer: parsedData.data.answer || '',
        fullData: fullData,
        isStreamEnd: false
      }
    }

    // 如果没有有效数据，返回空但不是结束
    return {
      answer: '',
      fullData: null,
      isStreamEnd: false
    }
  } catch (error) {
    console.error('流数据解析错误:', error)
    return {
      answer: '',
      fullData: null,
      isStreamEnd: false
    }
  }
}

// 复制到剪贴板
const copyToClipboard = async (text) => {
  try {
    await navigator.clipboard.writeText(text)
    $message.success('已复制到剪贴板')
  } catch (err) {
    // 如果navigator.clipboard不可用，使用传统方法
    const textarea = document.createElement('textarea')
    textarea.value = text
    document.body.appendChild(textarea)
    textarea.select()
    try {
      document.execCommand('copy')
      $message.success('已复制到剪贴板')
    } catch (e) {
      $message.error('复制失败')
    }
    document.body.removeChild(textarea)
  }
}

// 检查是否在底部
const isAtBottom = () => {
  if (!messagesRef.value) return false
  const { scrollTop, scrollHeight, clientHeight } = messagesRef.value
  return scrollHeight - scrollTop - clientHeight < 50 // 50px容差
}

// 滚动到底部
const scrollToBottom = async () => {
  await nextTick()
  if (messagesRef.value && !isUserScrolling.value) {
    messagesRef.value.scrollTop = messagesRef.value.scrollHeight
  }
}



// 处理用户滚动
const handleScroll = () => {
  if (!messagesRef.value) return

  // 如果用户滚动到底部，重置用户滚动状态
  if (isAtBottom()) {
    isUserScrolling.value = false
  } else {
    // 用户在中间位置滚动
    isUserScrolling.value = true
  }
}

// 监听流式输出内容变化，自动滚动到底部
watch(markdownContent, () => {
  if (isStreamIng.value) {
    scrollToBottom()
  }
})

// 监听消息变化，自动滚动到底部
watch(
  () => currentChat.value?.messages,
  () => {
    // 新消息时重置用户滚动状态并滚动到底部
    isUserScrolling.value = false
    scrollToBottom()
  },
  { deep: true }
)

const toggleSidebar = () => {
  isSidebarCollapsed.value = !isSidebarCollapsed.value
}

// 处理键盘事件
const handleKeyDown = (e) => {
  // 只处理回车键
  if (e.key !== 'Enter') return
  
  // 如果按住了 shift 键，允许换行
  if (e.shiftKey) return
  
  // 阻止默认的换行行为
  e.preventDefault()
  
  // 如果正在加载或者输入为空，不发送
  if (loading.value || !inputMessage.value.trim()) {
    return
  }
  
  // 发送消息
  handleSend()
}

onMounted(() => {
  loadChatList()

  // 添加滚动事件监听器
  nextTick(() => {
    if (messagesRef.value) {
      messagesRef.value.addEventListener('scroll', handleScroll)
    }
  })
})

// 在组件卸载时清理
onUnmounted(() => {
  if (controller.value) {
    controller.value.abort()
  }

  // 移除滚动事件监听器
  if (messagesRef.value) {
    messagesRef.value.removeEventListener('scroll', handleScroll)
  }
})
</script>

<style lang="scss" scoped>
.chat-container {
  height: 100%;
  display: flex;
  background: linear-gradient(135deg, var(--dify_bg_common), var(--dify_bg_secondary));
  gap: 16px;
  padding: 16px;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
      radial-gradient(circle at 20% 20%, rgba(var(--dify_color_primary_rgb), 0.03) 0%, transparent 50%),
      radial-gradient(circle at 80% 80%, rgba(var(--dify_color_primary_rgb), 0.03) 0%, transparent 50%);
    pointer-events: none;
  }
}

.chat-sidebar {
  width: 280px;
  display: flex;
  flex-direction: column;
  background-color: rgba(var(--dify_bg_secondary_rgb), 0.7);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.1);
  position: relative;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid rgba(var(--dify_border_rgb), 0.1);
  
  &.collapsed {
    width: 48px;
    
    .sidebar-header, .chat-history {
      display: none;
    }
    
    .sidebar-toggle {
      border-radius: 12px;
    }
  }
  
  .sidebar-header {
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid rgba(var(--dify_border_rgb), 0.1);
    
    .new-chat-btn {
      color: var(--dify_color_primary);
      border: none;
      background: rgba(var(--dify_color_primary_rgb), 0.1);
      backdrop-filter: blur(4px);
      transition: all 0.3s ease;
      
      &:hover {
        background: rgba(var(--dify_color_primary_rgb), 0.2);
        transform: translateY(-1px);
      }
    }
  }
  
  .sidebar-toggle {
    position: absolute;
    right: -16px;
    top: 50%;
    transform: translateY(-50%);
    width: 32px;
    height: 32px;
    background: var(--dify_bg_secondary);
    border: 1px solid rgba(var(--dify_border_rgb), 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    z-index: 1;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    
    &:hover {
      background: var(--dify_bg_hover);
      transform: translateY(-50%) scale(1.05);
    }

    :deep(svg) {
      width: 16px;
      height: 16px;
      transition: transform 0.3s ease;
    }
  }
  
  .chat-history {
    flex: 1;
    overflow-y: auto;
    padding: 16px;
    
    .history-item {
      padding: 12px 16px;
      margin-bottom: 8px;
      border-radius: 12px;
      cursor: pointer;
      display: flex;
      align-items: center;
      gap: 12px;
      color: var(--dify_text_secondary);
      transition: all 0.3s ease;
      border: 1px solid transparent;
      
      &:hover {
        background: rgba(var(--dify_bg_hover_rgb), 0.5);
        border-color: rgba(var(--dify_border_rgb), 0.1);
        transform: translateX(2px);
        button {
          opacity: 1;
        }
      }
      button {
        opacity: 0;
      }
      
      :deep(svg) {
        width: 18px;
        height: 18px;
        opacity: 0.8;
      }
      
      .truncate {
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        font-size: 14px;
      }
    }
  }
}

.chat-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: rgba(var(--dify_bg_secondary_rgb), 0.7);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  border: 1px solid rgba(var(--dify_border_rgb), 0.1);
  
  .chat-header {
    padding: 20px 24px;
    background: linear-gradient(to right, 
      rgba(var(--dify_bg_secondary_rgb), 0.8),
      rgba(var(--dify_bg_common_rgb), 0.8)
    );
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(var(--dify_border_rgb), 0.1);
    
    .header-content {
      background: rgba(var(--dify_bg_common_rgb), 0.8);
      padding: 16px 24px;
      border-radius: 12px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
      backdrop-filter: blur(4px);
      
      .chat-title {
        font-size: 18px;
        font-weight: 600;
        color: var(--dify_text_primary);
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }
    }
  }
  
  .chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 24px;
    
    &::-webkit-scrollbar {
      width: 6px;
    }
    
    &::-webkit-scrollbar-track {
      background: transparent;
    }
    
    &::-webkit-scrollbar-thumb {
      background: rgba(var(--dify_border_rgb), 0.3);
      border-radius: 3px;
      
      &:hover {
        background: rgba(var(--dify_text_secondary_rgb), 0.5);
      }
    }
    
    .empty-state {
      height: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      color: var(--dify_text_secondary);
      gap: 20px;
      
      p {
        font-size: 16px;
        margin: 0;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }
    }
    
    .message-item {
      display: flex;
      margin-bottom: 32px;
      
      &.user {
        flex-direction: row-reverse;
        
        .message-avatar {
          margin: 0 0 0 16px;
          background: rgba(var(--dify_bg_secondary_rgb), 0.8);
          border: 1px solid rgba(var(--dify_border_rgb), 0.1);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
          color: var(--dify_text_primary);
        }
        
        .message-content {
          width: 80%;
          align-items: flex-end;
          
          .message-bubble {
            background: #fff;
            border-radius: 4px 16px 16px 16px;
            border: 1px solid rgba(var(--dify_border_rgb), 0.1);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
            
            .message-text {
              color: var(--dify_text_primary);
              font-size: 14px;
              line-height: 1.6;
            }
          }
        }
      }
      
      &.assistant {
        .message-avatar {
          background: rgba(var(--dify_bg_secondary_rgb), 0.8);
          border: 1px solid rgba(var(--dify_border_rgb), 0.1);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
          color: var(--dify_text_primary);
        }
        
        .message-content {
          width: 80%;

          .message-bubble {
            background: #fff;
            border-radius: 4px 16px 16px 16px;
            border: 1px solid rgba(var(--dify_border_rgb), 0.1);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);

            .message-text {
              color: var(--dify_text_primary);
              font-size: 14px;
              line-height: 1.6;
            }
          }

          .disposal-message {
            padding: 0;

            :deep(.disposal-card) {
              margin: 0;
              box-shadow: 0 4px 24px rgba(0, 0, 0, 0.1);
              border: 1px solid rgba(var(--dify_border_rgb), 0.2);
              transition: all 0.3s ease;

              &:hover {
                transform: translateY(-1px);
                box-shadow: 0 6px 28px rgba(0, 0, 0, 0.15);
              }
            }
          }

          .script-execution-message {
            padding: 0;

            :deep(.script-execution-card) {
              margin: 0;
              box-shadow: 0 4px 24px rgba(0, 0, 0, 0.1);
              border: 1px solid rgba(var(--dify_border_rgb), 0.2);
              transition: all 0.3s ease;

              &:hover {
                transform: translateY(-1px);
                box-shadow: 0 6px 28px rgba(0, 0, 0, 0.15);
              }
            }
          }

          .typing-message {
            display: flex;
            align-items: flex-end;

            .typing-content {
              flex: 1;
              line-height: 1.6;
              white-space: pre-wrap;
              word-break: break-word;

              // 格式化内容样式
              :deep(strong) {
                font-weight: 600;
                color: var(--dify_text_primary);
              }

              :deep(em) {
                font-style: italic;
                color: var(--dify_text_secondary);
              }

              :deep(code) {
                background-color: #f5f5f5;
                padding: 2px 4px;
                border-radius: 3px;
                font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
                font-size: 0.9em;
              }

              :deep(br) {
                line-height: 1.8;
              }
            }
          }

          .formatted-message {
            line-height: 1.6;
            white-space: pre-wrap;
            word-break: break-word;

            // 格式化内容样式
            :deep(strong) {
              font-weight: 600;
              color: var(--dify_text_primary);
            }

            :deep(em) {
              font-style: italic;
              color: var(--dify_text_secondary);
            }

            :deep(code) {
              background-color: #f5f5f5;
              padding: 2px 4px;
              border-radius: 3px;
              font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
              font-size: 0.9em;
            }

            :deep(br) {
              line-height: 1.8;
            }

            // 列表样式
            :deep(div) {
              margin-bottom: 4px;
            }
          }

          // .typing-cursor {
          //   margin-left: 2px;
          //   font-weight: bold;
          //   color: var(--dify_text_primary);
          //   animation: blink 1s infinite;
          // }
        }
      }
      
      .message-avatar {
        width: 40px;
        height: 40px;
        margin-right: 16px;
        border-radius: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;
        transition: all 0.3s ease;
        backdrop-filter: blur(4px);
        
        :deep(svg) {
          width: 20px;
          height: 20px;
        }
      }
      
      .message-content {
        width: 80%;
        display: flex;
        flex-direction: column;
        
        .message-bubble {
          padding: 16px 20px;
          transition: all 0.3s ease;
          backdrop-filter: blur(4px);
          
          &.loading {
            min-width: 200px;
            // background: rgba(var(--dify_bg_secondary_rgb), 0.8);
            border-radius: 4px 16px 16px 16px;
            border: 1px solid rgba(var(--dify_border_rgb), 0.1);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
            animation: pulse 2s infinite;
          }
        }
        
        .message-text {
          white-space: pre-wrap;
          word-break: break-word;
          line-height: 1.6;
          font-size: 14px;
        }
        
        .message-actions {
          margin-top: 8px;
          display: flex;
          justify-content: start;
          opacity: 0;
          transition: all 0.3s ease;
          
          :deep(.n-button) {
            padding: 6px 12px;
            font-size: 12px;
            backdrop-filter: blur(4px);
            border: 1px solid rgba(var(--dify_border_rgb), 0.1);
            transition: all 0.3s ease;
            
            &:hover {
              transform: translateY(-1px);
              box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            }
            
            .icon {
              width: 14px;
              height: 14px;
            }
          }
        }
        
        &:hover {
          .message-actions {
            opacity: 1;
            transform: translateY(-2px);
          }
        }
      }
    }
  }
  
  .chat-input-wrapper {
    padding: 20px;
    background: rgba(var(--dify_bg_common_rgb), 0.8);
    backdrop-filter: blur(10px);
    border-top: 1px solid rgba(var(--dify_border_rgb), 0.1);
    width: 100%;
    display: flex;
    justify-content: center;

    .chat-input {
      width: 80%;
      gap: 16px;
      background: rgba(var(--dify_bg_secondary_rgb), 0.8);
      padding: 16px 20px;
      border-radius: 16px;
      box-shadow: 0 4px 24px rgba(0, 0, 0, 0.1);
      display: flex;
      align-items: flex-end;
      backdrop-filter: blur(4px);
      border: 1px solid rgba(var(--dify_border_rgb), 0.1);
      transition: all 0.3s ease;
      
      &:focus-within {
        transform: translateY(-1px);
        box-shadow: 0 6px 28px rgba(0, 0, 0, 0.15);
      }
    }
  }
}

:deep(.n-button:not(.n-button--primary-type)) {
  color: var(--dify_text_primary) !important;
  background: rgba(var(--dify_bg_common_rgb), 0.8) !important;
  border-color: rgba(var(--dify_border_rgb), 0.1) !important;
  backdrop-filter: blur(4px);
  transition: all 0.3s ease;

  &:hover {
    color: var(--dify_color_primary) !important;
    border-color: rgba(var(--dify_color_primary_rgb), 0.2) !important;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
}

:deep(.n-input) {
  background: rgba(var(--dify_bg_common_rgb), 0.8) !important;
  backdrop-filter: blur(4px);
  transition: all 0.3s ease;
  
  .n-input__input-el {
    color: var(--dify_text_primary) !important;
  }
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
  100% {
    opacity: 1;
  }
}

@keyframes blink {
  0%, 50% {
    opacity: 1;
  }
  51%, 100% {
    opacity: 0;
  }
}
</style> 